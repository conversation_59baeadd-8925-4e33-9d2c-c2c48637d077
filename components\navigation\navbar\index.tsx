"use client";

import Image from "next/image";
import React, { useState } from "react";
import { Menu, X, Bell, Globe } from "lucide-react";
import { Button } from "@/components/ui/button";

const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="border-b bg-white px-6 py-3 shadow-sm shrink-0">
      <div className="flex items-center justify-between">
        {/* Left: Logo (only visible on mobile) */}
        <div className="text-xl font-semibold text-gray-900 ">AI Interview</div>

        {/* Left: Empty space on desktop to push content right */}
        <div className="hidden md:block"></div>

        {/* Right: Nav Items */}
        <div className="hidden items-center gap-4 md:flex">
          {/* Notification Icon */}
          <Bell className="h-5 w-5 text-gray-600 cursor-pointer" />

          {/* Language */}
          <div className="flex items-center gap-1 text-sm text-gray-700">
            <Globe className="h-4 w-4" />
            English
          </div>

          {/* Profile */}
          <div className="flex items-center gap-2 rounded-full bg-gray-100 px-3 py-1">
            <span className="text-sm font-medium text-gray-800">Hammad M</span>
            <span className="rounded-full bg-purple-100 px-2 py-0.5 text-xs text-purple-600">
              Free
            </span>
          </div>
        </div>

        {/* Mobile menu toggle */}
        <div className="md:hidden">
          <button onClick={toggleMobileMenu}>
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile dropdown (optional) */}
      {isMobileMenuOpen && (
        <div className="mt-3 flex flex-col gap-3 md:hidden">
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            English
          </div>
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
          </div>
          <div className="flex items-center gap-2">
            <span>Hammad M</span>
            <span className="rounded-full bg-purple-100 px-2 py-0.5 text-xs text-purple-600">
              Free
            </span>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
