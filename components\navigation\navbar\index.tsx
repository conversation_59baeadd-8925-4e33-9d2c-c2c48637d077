"use client";

import Image from "next/image";
import React, { useState } from "react";
import { Menu, X, BellDot, Globe, ChevronDown, Bell } from "lucide-react";
import { Button } from "@/components/ui/button";

const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="border-b bg-white px-6 py-5 shrink-0">
      <div className="flex items-center justify-between">
        <div className="text-xl font-semibold text-gray-900 ">AI Interview</div>

        <div className="hidden items-center gap-6 md:flex">
          {/* Notification Icon */}
          <BellDot className="h-8 w-7  text-gray-700 cursor-pointer hover:text-gray-800 transition-colors" />

          {/* Language Selector */}
          <div className="flex items-center gap-2 text-sm text-gray-700 cursor-pointer bg-gray-50 py-4 px-6 rounded-full transition-colors">
            <Globe className="h-6 w-6 " />
            <span className="font-bold">English</span>
            <ChevronDown className="h-5 w-5 rounded-full bg-[#dddae5] border border-[#aba6bb] text-[#aba6bb]" />
          </div>

          {/* Profile Section */}
          <div className="flex items-center gap-3 cursor-pointer bg-gray-50 rounded-full px-8 py-3 transition-colors">
            {/* Avatar */}
            <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-500 p-1"></span>
            </div>

            {/* User Info */}
            <div className="flex flex-col">
              <span className="text-sm font-bold text-gray-900">Hammad M</span>
              <span className="text-xs text-purple-600">Free</span>
            </div>

            {/* Dropdown Arrow */}
            <ChevronDown className="h-5 w-5 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]" />
          </div>
        </div>

        {/* Mobile menu toggle */}
        <div className="md:hidden">
          <button onClick={toggleMobileMenu}>
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile dropdown (optional) */}
      {isMobileMenuOpen && (
        <div className="mt-3 flex flex-col gap-3 md:hidden">
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            English
          </div>
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
          </div>
          <div className="flex items-center gap-2">
            <span>Hammad M</span>
            <span className="rounded-full bg-purple-100 px-2 py-0.5 text-xs text-purple-600">
              Free
            </span>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
