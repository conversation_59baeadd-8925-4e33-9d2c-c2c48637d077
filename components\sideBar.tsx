"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import LOGO from "../public/images/logo-light.svg";
import { LayoutDashboard, BriefcaseBusiness } from "lucide-react";

const sidebarItems = [
  {
    label: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    label: "Job Posts",
    href: "/job-posts",
    icon: BriefcaseBusiness,
  },
];

const Sidebar = () => {
  const pathname = usePathname();

  return (
    <aside className="hidden md:flex w-60 h-full bg-white border-r p-6 flex-col shrink-0">
      {/* Logo */}
      <div className="flex items-center gap-2 mb-10">
        <Image src={LOGO} alt="Logo" />
      </div>

      {/* Navigation */}
      <nav className="flex flex-col gap-4">
        {sidebarItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <Link
              key={item.label}
              href={item.href}
              className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group
                ${
                  isActive
                    ? "bg-[#f3e8ff] text-[#7b2cbf]"
                    : "text-[#bab4d0] hover:bg-gray-100"
                }`}
            >
              <div className={`  ${isActive ? "" : ""} transition-all`}>
                <Icon
                  className={`w-5 h-5 ${
                    isActive ? "text-[#7b2cbf]" : "text-[#bab4d0]"
                  }`}
                />
              </div>
              <span className="text-base font-medium">{item.label}</span>
            </Link>
          );
        })}
      </nav>
    </aside>
  );
};

export default Sidebar;
